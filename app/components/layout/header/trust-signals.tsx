import React from "react";
import { Phone, Shield } from "lucide-react";
import { Badge } from "~/components/ui/badge";
import type { TrustSignalsProps } from "./types";

export function TrustSignals({ phone, certifications, securityBadge }: TrustSignalsProps) {
  if (!phone && !certifications?.length && !securityBadge) return null;

  return (
    <div className="hidden lg:flex items-center gap-4 text-sm text-muted-foreground">
      {phone && (
        <div className="flex items-center gap-1">
          <Phone className="h-4 w-4" />
          <span>{phone}</span>
        </div>
      )}
      {securityBadge && (
        <div className="flex items-center gap-1">
          <Shield className="h-4 w-4 text-green-600" />
          <span className="text-green-600">Secure</span>
        </div>
      )}
      {certifications?.map((cert, i) => (
        <Badge key={`${cert}-${i}`} variant="outline" className="text-xs">
          {cert}
        </Badge>
      ))}
    </div>
  );
}
